package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DataAccessParamsRepository extends ResourceRepository<DataAccessParams, Long> {
    List<DataAccessParams> findByDataAccessOrderBySequenceAscIdAsc(DataAccess dataAccess);

    @Query("SELECT COALESCE(MAX(p.sequence), 0) FROM DataAccessParams p WHERE p.dataAccess = :dataAccess")
    Integer findMaxSequenceByDataAccess(@Param("dataAccess") DataAccess dataAccess);
}
